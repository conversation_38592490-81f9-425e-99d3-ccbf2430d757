/**
 * Timezone Helper - Provides consistent timezone handling across the application
 * Ensures all timestamps are displayed consistently regardless of server timezone
 */
import moment from "moment";

export class TimezoneHelper {
  // Default timezone for the application (Indian Standard Time)
  private static readonly DEFAULT_TIMEZONE = "Asia/Kolkata";

  /**
   * Format a date to a consistent timezone and format
   * @param date - Date to format (can be Date object, string, or timestamp)
   * @param timezone - Target timezone (defaults to IST)
   * @param options - Intl.DateTimeFormatOptions for formatting
   * @returns Formatted date string
   */
  static formatDate(
    date: Date | string | number,
    timezone: string = TimezoneHelper.DEFAULT_TIMEZONE,
    options?: Intl.DateTimeFormatOptions
  ): string {
    if (!date) return "N/A";

    try {
      const dateObj = new Date(date);

      // Check if date is valid
      if (isNaN(dateObj.getTime())) {
        return "Invalid Date";
      }

      const defaultOptions: Intl.DateTimeFormatOptions = {
        day: "2-digit",
        month: "2-digit",
        year: "numeric",
        hour: "2-digit",
        minute: "2-digit",
        hour12: true,
        timeZone: timezone,
      };

      const formatOptions = { ...defaultOptions, ...options };

      return dateObj.toLocaleString("en-GB", formatOptions).replace(/\//g, "-");
    } catch (error) {
      console.error("Error formatting date:", error);
      return "Invalid Date";
    }
  }

  /**
   * Format date for CSV export (consistent timezone handling)
   * @param date - Date to format
   * @returns Formatted date string for CSV with consistent timezone
   */
  static formatForCSV(date: Date | string | number): string {
    if (!date) return "N/A";

    try {
      // Parse the date and convert to IST consistently
      const momentDate = moment.utc(date);

      if (!momentDate.isValid()) {
        return "Invalid Date";
      }

      // Convert UTC to IST (UTC+5:30) and format with 12-hour format
      const istDate = momentDate.utcOffset("+05:30");
      return istDate.format("DD-MM-YYYY hh:mm:ss A");
    } catch (error) {
      console.error("Error formatting date for CSV:", error);
      return "Invalid Date";
    }
  }

  /**
   * Format date for Excel export (consistent timezone handling)
   * @param date - Date to format
   * @returns Formatted date string for Excel with consistent timezone
   */
  static formatForExcel(date: Date | string | number): string {
    if (!date) return "N/A";

    try {
      // Parse the date and convert to IST consistently
      const momentDate = moment.utc(date);

      if (!momentDate.isValid()) {
        return "Invalid Date";
      }

      // Convert UTC to IST (UTC+5:30) and format with 12-hour format
      const istDate = momentDate.utcOffset("+05:30");
      return istDate.format("DD-MM-YYYY hh:mm:ss A");
    } catch (error) {
      console.error("Error formatting date for Excel:", error);
      return "Invalid Date";
    }
  }

  /**
   * Format date for display in UI (consistent format)
   * @param date - Date to format
   * @returns Formatted date string for UI display
   */
  static formatForDisplay(date: Date | string | number): string {
    return TimezoneHelper.formatDate(date, TimezoneHelper.DEFAULT_TIMEZONE, {
      day: "2-digit",
      month: "short",
      year: "numeric",
      hour: "2-digit",
      minute: "2-digit",
      hour12: true,
    });
  }

  /**
   * Get current timestamp in IST
   * @returns Current date formatted in IST
   */
  static getCurrentIST(): string {
    return TimezoneHelper.formatDate(new Date());
  }

  /**
   * Convert UTC date to IST
   * @param utcDate - UTC date
   * @returns IST formatted date
   */
  static utcToIST(utcDate: Date | string | number): string {
    return TimezoneHelper.formatDate(utcDate, "Asia/Kolkata");
  }

  /**
   * Get timezone offset for IST
   * @returns IST timezone offset string
   */
  static getISTOffset(): string {
    const now = new Date();
    const istTime = new Date(
      now.toLocaleString("en-US", { timeZone: "Asia/Kolkata" })
    );
    const utcTime = new Date(now.toLocaleString("en-US", { timeZone: "UTC" }));
    const offset = (istTime.getTime() - utcTime.getTime()) / (1000 * 60 * 60);
    return `UTC${offset >= 0 ? "+" : ""}${offset}:30`;
  }

  /**
   * Format date for export with explicit timezone handling
   * This method ensures consistent formatting regardless of server timezone
   * @param date - Date to format
   * @param includeTimezone - Whether to include timezone info in output
   * @returns Formatted date string with consistent timezone
   */
  static formatForExportConsistent(
    date: Date | string | number,
    includeTimezone: boolean = false
  ): string {
    if (!date) return "N/A";

    try {
      // Always treat input as UTC and convert to IST
      const momentDate = moment.utc(date);

      if (!momentDate.isValid()) {
        return "Invalid Date";
      }

      // Convert to IST (UTC+5:30)
      const istDate = momentDate.utcOffset("+05:30");

      const formatted = istDate.format("DD-MM-YYYY HH:mm:ss");
      return includeTimezone ? `${formatted} IST` : formatted;
    } catch (error) {
      console.error("Error formatting date for export:", error);
      return "Invalid Date";
    }
  }
}

export default TimezoneHelper;
