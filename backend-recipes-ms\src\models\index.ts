"use strict";

import fs from "fs";
import path from "path";
import Sequelize, { Op, QueryTypes } from "sequelize";
const basename = path.basename(__filename);

const config = global.db;
const db: any = {};
let sequelize: any;
if (global.config.use_env_variable) {
  sequelize = new Sequelize.Sequelize(global.config.use_env_variable, config);
} else {
  sequelize = new Sequelize.Sequelize(
    config.database,
    config.username,
    config.password,
    config
  );
}
sequelize
  .authenticate()
  .then(() => { })
  .catch((err: Error) => { });

// Load models in specific order to avoid dependency issues
const modelFiles = [
  "User.ts",
  "Item.ts", // Load Item first since other models depend on it
  "Category.ts",
  "FoodAttributes.ts",
  "RecipeMeasure.ts",
  "Ingreditant.ts", // Note: typo in filename but correct modelName
  "Settings.ts",
  "Analytics.ts",
  "ContactUs.ts",
  // Junction tables last
  "IngredientAttributes.ts",
  "IngredientCategory.ts",
  "IngredientConversion.ts",
  "RecipeAttributes.ts",
  "RecipeBookmarks.ts",
  "RecipeCategory.ts",
  "RecipeHistory.ts",
  "RecipeIngredients.ts",
  "RecipeResources.ts",
  "RecipeSteps.ts",
  "RecipeUser.ts",
  "Recipe.ts",
  // "Recipe_old.ts", // Commented out to prevent model conflicts
];

// Load models in the specified order
modelFiles.forEach((file) => {
  // Try both .ts and .js extensions
  const tsPath = path.join(__dirname, file);
  const jsPath = path.join(__dirname, file.replace(".ts", ".js"));

  let filePath = tsPath;
  if (!fs.existsSync(tsPath) && fs.existsSync(jsPath)) {
    filePath = jsPath;
  }

  if (fs.existsSync(filePath)) {
    try {
      // eslint-disable-next-line @typescript-eslint/no-require-imports
      const model = require(filePath);

      // Handle both default exports and named exports
      let modelClass = model.default;

      if (!modelClass) {
        const modelKey = Object.keys(model).find(
          (key) =>
            model[key] &&
            typeof model[key] === "function" &&
            model[key].prototype instanceof Sequelize.Model
        );
        if (modelKey) {
          modelClass = model[modelKey];
        }
      }

      if (modelClass && modelClass.name) {
        db[modelClass.name] = modelClass;
      }
    } catch (error) {
      console.error(`❌ Error loading model ${file}:`, error);
    }
  }
});

// Load any remaining models that weren't in the list
// Exclude Recipe_old.ts to prevent model conflicts
const excludedFiles = ["Recipe_old.ts"];
fs.readdirSync(__dirname)
  .filter((file) => {
    return (
      file.indexOf(".") !== 0 &&
      file !== basename &&
      file.slice(-3) === ".ts" &&
      !modelFiles.includes(file) &&
      !excludedFiles.includes(file)
    );
  })
  .forEach((file) => {
    try {
      // eslint-disable-next-line @typescript-eslint/no-require-imports
      const model = require(path.join(__dirname, file));
      // Handle both default exports and named exports
      let modelClass = model.default;

      if (!modelClass) {
        const modelKey = Object.keys(model).find(
          (key) =>
            model[key] &&
            typeof model[key] === "function" &&
            model[key].prototype instanceof Sequelize.Model
        );
        if (modelKey) {
          modelClass = model[modelKey];
        }
      }

      if (modelClass && modelClass.name) {
        db[modelClass.name] = modelClass;
      }
    } catch (error) {
      console.error(`❌ Error loading additional model ${file}:`, error);
    }
  });

// Set up model associations
Object.keys(db).forEach((modelName) => {
  if (db[modelName].associate) {
    try {
      db[modelName].associate(db);
    } catch (error) {
      console.error(
        `❌ Error setting up associations for ${modelName}:`,
        error
      );
    }
  }
});
db.sequelize = sequelize;
db.Sequelize = Sequelize;
db.Op = Op;
db.QueryTypes = QueryTypes;

export { db, sequelize, QueryTypes };
