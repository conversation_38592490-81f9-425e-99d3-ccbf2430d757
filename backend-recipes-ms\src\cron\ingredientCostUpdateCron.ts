import { sequelize } from "../models";
import { Ingredient } from "../models/Ingreditant";
import { handleIngredientCostUpdate } from "../helper/cost-freshness.helper";

interface IngredientCostUpdateStats {
  totalIngredientsChecked: number;
  ingredientsWithUpdatedCosts: number;
  affectedRecipes: number;
  recipesUpdated: number;
  recipeUpdatesFailed: number;
  organizationsProcessed: string[];
  errors: string[];
  executionTime: number;
}

/**
 * Cron job to handle ingredient cost updates and cascade to affected recipes
 * This job checks for ingredients with recently updated costs and triggers recipe recalculations
 */
export const ingredientCostUpdateCronJob = async (): Promise<IngredientCostUpdateStats> => {
  const startTime = Date.now();
  const stats: IngredientCostUpdateStats = {
    totalIngredientsChecked: 0,
    ingredientsWithUpdatedCosts: 0,
    affectedRecipes: 0,
    recipesUpdated: 0,
    recipeUpdatesFailed: 0,
    organizationsProcessed: [],
    errors: [],
    executionTime: 0
  };

  try {
    console.log('Starting ingredient cost update cron job...');

    // Get all active organizations with ingredients
    const organizations = await getActiveOrganizationsWithIngredients();
    stats.organizationsProcessed = organizations;

    console.log(`Processing ${organizations.length} organizations for ingredient cost updates`);

    // Process each organization separately
    for (const organizationId of organizations) {
      try {
        const orgStats = await processOrganizationIngredientUpdates(organizationId);

        stats.totalIngredientsChecked += orgStats.totalIngredientsChecked;
        stats.ingredientsWithUpdatedCosts += orgStats.ingredientsWithUpdatedCosts;
        stats.affectedRecipes += orgStats.affectedRecipes;
        stats.recipesUpdated += orgStats.recipesUpdated;
        stats.recipeUpdatesFailed += orgStats.recipeUpdatesFailed;
        stats.errors.push(...orgStats.errors);

        console.log(`Organization ${organizationId}: ${orgStats.ingredientsWithUpdatedCosts} ingredients processed, ${orgStats.recipesUpdated} recipes updated`);

      } catch (error: any) {
        const errorMsg = `Failed to process ingredient updates for organization ${organizationId}: ${error.message}`;
        stats.errors.push(errorMsg);
        console.error(errorMsg);
      }
    }

    stats.executionTime = Date.now() - startTime;

    // Log summary
    console.log('Ingredient cost update cron job completed:');
    console.log(`- Total ingredients checked: ${stats.totalIngredientsChecked}`);
    console.log(`- Ingredients with updated costs: ${stats.ingredientsWithUpdatedCosts}`);
    console.log(`- Affected recipes: ${stats.affectedRecipes}`);
    console.log(`- Recipes updated: ${stats.recipesUpdated}`);
    console.log(`- Recipe updates failed: ${stats.recipeUpdatesFailed}`);
    console.log(`- Organizations processed: ${stats.organizationsProcessed.length}`);
    console.log(`- Execution time: ${stats.executionTime}ms`);

    if (stats.errors.length > 0) {
      console.log(`- Errors encountered: ${stats.errors.length}`);
      stats.errors.forEach(error => console.error(`  - ${error}`));
    }

    return stats;

  } catch (error: any) {
    stats.executionTime = Date.now() - startTime;
    stats.errors.push(`Ingredient cost update cron job failed: ${error.message}`);
    console.error('Ingredient cost update cron job failed:', error);
    throw error;
  }
};

/**
 * Get all active organizations that have ingredients with recent cost updates
 */
const getActiveOrganizationsWithIngredients = async (): Promise<string[]> => {
  try {
    // Get organizations with ingredients that have been updated in the last hour
    // This helps identify which organizations might need recipe cost recalculations
    const query = `
      SELECT DISTINCT i.organization_id
      FROM mo_ingredient i
      WHERE i.ingredient_status = 'active'
        AND i.organization_id IS NOT NULL
        AND i.organization_id != ''
        AND i.updated_at >= DATE_SUB(NOW(), INTERVAL 1 HOUR)
      ORDER BY i.organization_id
    `;

    const results = await sequelize.query(query, {
      type: sequelize.QueryTypes.SELECT
    }) as Array<{ organization_id: string }>;

    return results.map(row => row.organization_id);

  } catch (error) {
    console.error('Error getting organizations with ingredient updates:', error);
    throw error;
  }
};

/**
 * Process ingredient cost updates for a specific organization
 */
const processOrganizationIngredientUpdates = async (organizationId: string): Promise<{
  totalIngredientsChecked: number;
  ingredientsWithUpdatedCosts: number;
  affectedRecipes: number;
  recipesUpdated: number;
  recipeUpdatesFailed: number;
  errors: string[];
}> => {
  const orgStats = {
    totalIngredientsChecked: 0,
    ingredientsWithUpdatedCosts: 0,
    affectedRecipes: 0,
    recipesUpdated: 0,
    recipeUpdatesFailed: 0,
    errors: [] as string[]
  };

  try {
    // Get ingredients with recent cost updates (within last hour)
    const ingredients = await Ingredient.findAll({
      where: {
        organization_id: organizationId,
        ingredient_status: 'active'
      },
      attributes: ['id', 'ingredient_name', 'updated_at', 'ingredient_cost'],
      order: [['id', 'ASC']]
    });

    orgStats.totalIngredientsChecked = ingredients.length;

    if (ingredients.length === 0) {
      return orgStats;
    }

    console.log(`Checking ${ingredients.length} ingredients for organization ${organizationId}`);

    // Filter ingredients that have been updated recently (within last hour)
    const recentlyUpdatedIngredients = ingredients.filter((ingredient: any) => {
      const updatedAt = new Date(ingredient.updated_at);
      const oneHourAgo = new Date(Date.now() - 60 * 60 * 1000);
      return updatedAt > oneHourAgo;
    });

    orgStats.ingredientsWithUpdatedCosts = recentlyUpdatedIngredients.length;

    // Process each recently updated ingredient
    for (const ingredient of recentlyUpdatedIngredients) {
      try {
        console.log(`Processing ingredient ${ingredient.id} (${ingredient.ingredient_name}) with cost $${ingredient.ingredient_cost}`);

        // Use the cost-freshness helper to handle ingredient cost updates
        const updateResult = await handleIngredientCostUpdate(
          ingredient.id,
          organizationId,
          {
            autoUpdateRecipes: true, // Enable auto-update for affected recipes
            transaction: undefined
          }
        );

        if (updateResult.success) {
          orgStats.affectedRecipes += updateResult.affectedRecipes.length;

          // Count successful and failed recipe updates
          updateResult.updateResults.forEach(result => {
            if (result.success) {
              orgStats.recipesUpdated++;
            } else {
              orgStats.recipeUpdatesFailed++;
              orgStats.errors.push(`Recipe ${result.recipeId} update failed: ${result.errors.join(', ')}`);
            }
          });

          console.log(`Ingredient ${ingredient.id} update cascaded to ${updateResult.affectedRecipes.length} recipes`);
        } else {
          orgStats.errors.push(`Failed to process ingredient ${ingredient.id} cost update`);
        }

      } catch (error: any) {
        orgStats.recipeUpdatesFailed++;
        const errorMsg = `Error processing ingredient ${ingredient.id}: ${error.message}`;
        orgStats.errors.push(errorMsg);
        console.error(errorMsg);
      }
    }

    return orgStats;

  } catch (error: any) {
    orgStats.errors.push(`Organization ingredient processing failed: ${error.message}`);
    throw error;
  }
};

/**
 * Manual trigger for ingredient cost update processing
 */
export const triggerManualIngredientCostUpdate = async (organizationId?: string): Promise<IngredientCostUpdateStats> => {
  console.log('Manual ingredient cost update triggered');

  if (organizationId) {
    // Process specific organization only
    const startTime = Date.now();
    const orgStats = await processOrganizationIngredientUpdates(organizationId);

    return {
      totalIngredientsChecked: orgStats.totalIngredientsChecked,
      ingredientsWithUpdatedCosts: orgStats.ingredientsWithUpdatedCosts,
      affectedRecipes: orgStats.affectedRecipes,
      recipesUpdated: orgStats.recipesUpdated,
      recipeUpdatesFailed: orgStats.recipeUpdatesFailed,
      organizationsProcessed: [organizationId],
      errors: orgStats.errors,
      executionTime: Date.now() - startTime
    };
  } else {
    // Process all organizations
    return await ingredientCostUpdateCronJob();
  }
};

export default {
  ingredientCostUpdateCronJob,
  triggerManualIngredientCostUpdate
};
